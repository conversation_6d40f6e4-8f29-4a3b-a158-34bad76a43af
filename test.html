<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test User Agent Switcher</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .info-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .user-agent {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            word-break: break-all;
            font-family: monospace;
            font-size: 14px;
        }
        
        .detection {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .refresh-btn {
            background: #4285f4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        
        .refresh-btn:hover {
            background: #3367d6;
        }
        
        .test-links {
            margin: 20px 0;
        }
        
        .test-links a {
            display: inline-block;
            margin: 5px 10px;
            padding: 8px 15px;
            background: #6c757d;
            color: white;
            text-decoration: none;
            border-radius: 3px;
        }
        
        .test-links a:hover {
            background: #545b62;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Test User Agent Switcher</h1>
        
        <div class="info-box">
            <h3>Instructions :</h3>
            <ol>
                <li>Installez l'extension User Agent Switcher dans Chrome</li>
                <li>Cliquez sur l'icône de l'extension et sélectionnez un navigateur/OS</li>
                <li>Cliquez sur "Appliquer"</li>
                <li>Actualisez cette page pour voir le changement</li>
            </ol>
        </div>
        
        <div class="user-agent">
            <strong>User Agent actuel :</strong><br>
            <span id="userAgent"></span>
        </div>
        
        <div class="detection">
            <strong>Détection automatique :</strong><br>
            <span id="detection"></span>
        </div>
        
        <div style="text-align: center;">
            <button class="refresh-btn" onclick="location.reload()">🔄 Actualiser</button>
            <button class="refresh-btn" onclick="detectUserAgent()">🔍 Re-détecter</button>
        </div>
        
        <div class="test-links">
            <h3>Liens de test externes :</h3>
            <a href="https://httpbin.org/user-agent" target="_blank">HTTPBin User-Agent</a>
            <a href="https://www.whatismybrowser.com/" target="_blank">What Is My Browser</a>
            <a href="https://www.useragentstring.com/" target="_blank">User Agent String</a>
            <a href="https://developers.whatismybrowser.com/useragents/parse/" target="_blank">Parse User Agent</a>
        </div>
        
        <div class="info-box">
            <h3>User Agents de test disponibles :</h3>
            <ul>
                <li><strong>Chrome Windows :</strong> Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36...</li>
                <li><strong>Firefox macOS :</strong> Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:121.0)...</li>
                <li><strong>Safari macOS :</strong> Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15...</li>
                <li><strong>Edge Linux :</strong> Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36...</li>
            </ul>
        </div>
    </div>

    <script>
        function detectUserAgent() {
            const userAgent = navigator.userAgent;
            document.getElementById('userAgent').textContent = userAgent;
            
            // Détection simple du navigateur et de l'OS
            let browser = 'Inconnu';
            let os = 'Inconnu';
            
            // Détection du navigateur
            if (userAgent.includes('Chrome') && !userAgent.includes('Edg')) {
                browser = 'Chrome';
            } else if (userAgent.includes('Firefox')) {
                browser = 'Firefox';
            } else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
                browser = 'Safari';
            } else if (userAgent.includes('Edg')) {
                browser = 'Edge';
            }
            
            // Détection de l'OS
            if (userAgent.includes('Windows')) {
                os = 'Windows';
            } else if (userAgent.includes('Macintosh') || userAgent.includes('Mac OS')) {
                os = 'macOS';
            } else if (userAgent.includes('Linux')) {
                os = 'Linux';
            }
            
            document.getElementById('detection').innerHTML = `
                <strong>Navigateur détecté :</strong> ${browser}<br>
                <strong>OS détecté :</strong> ${os}<br>
                <strong>Timestamp :</strong> ${new Date().toLocaleString()}
            `;
        }
        
        // Détecter au chargement de la page
        detectUserAgent();
        
        // Afficher des informations supplémentaires
        console.log('User Agent complet:', navigator.userAgent);
        console.log('Platform:', navigator.platform);
        console.log('App Name:', navigator.appName);
        console.log('App Version:', navigator.appVersion);
    </script>
</body>
</html>
