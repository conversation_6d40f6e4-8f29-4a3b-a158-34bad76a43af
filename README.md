# User Agent Switcher - Extension Chrome

Une extension Chrome simple et élégante pour changer l'user agent de votre navigateur avec un tableau de sélection intuitif.

## Fonctionnalités

- **Interface en tableau** : Sélection facile avec navigateurs en vertical (Chrome, Firefox, Safari, Edge) et OS en horizontal (Windows, macOS, Linux)
- **Manifest V3** : Compatible avec les dernières spécifications Chrome
- **Persistance** : Sauvegarde automatique de votre sélection
- **Indicateur visuel** : Badge sur l'icône quand un user agent personnalisé est actif
- **Reset facile** : Bouton pour revenir au user agent par défaut

## Installation

### Méthode 1 : Installation en mode développeur

1. Téléchargez ou clonez ce repository
2. Ouvrez Chrome et allez dans `chrome://extensions/`
3. Activez le "Mode développeur" en haut à droite
4. C<PERSON>z sur "Charger l'extension non empaquetée"
5. Sélectionnez le dossier contenant les fichiers de l'extension
6. L'extension apparaîtra dans votre barre d'outils

### Méthode 2 : Création des icônes (optionnel)

Si vous voulez créer vos propres icônes :

1. Ouvrez le fichier `create_icons.html` dans votre navigateur
2. Les icônes se téléchargeront automatiquement
3. Placez les fichiers `icon16.png`, `icon48.png`, et `icon128.png` dans le dossier `icons/`

## Utilisation

1. Cliquez sur l'icône de l'extension dans la barre d'outils
2. Sélectionnez la combinaison navigateur/OS souhaitée dans le tableau
3. Cliquez sur "Appliquer"
4. Un badge "ON" apparaîtra sur l'icône pour indiquer qu'un user agent personnalisé est actif
5. Pour revenir au user agent par défaut, cliquez sur "Reset"

## User Agents inclus

### Chrome
- **Windows** : Chrome 120 sur Windows 10
- **macOS** : Chrome 120 sur macOS 10.15.7
- **Linux** : Chrome 120 sur Linux x86_64

### Firefox
- **Windows** : Firefox 121 sur Windows 10
- **macOS** : Firefox 121 sur macOS 10.15
- **Linux** : Firefox 121 sur Linux x86_64

### Safari
- **macOS** : Safari 17.1 sur macOS 10.15.7
- *Note : Safari n'est disponible que sur macOS*

### Edge
- **Windows** : Edge 120 sur Windows 10
- **macOS** : Edge 120 sur macOS 10.15.7
- **Linux** : Edge 120 sur Linux x86_64

## Structure des fichiers

```
user-agent-switcher/
├── manifest.json          # Configuration de l'extension
├── popup.html             # Interface utilisateur
├── popup.js               # Logique de l'interface
├── background.js          # Service worker
├── icons/                 # Icônes de l'extension
│   ├── icon16.png
│   ├── icon48.png
│   └── icon128.png
├── create_icons.html      # Générateur d'icônes (optionnel)
└── README.md             # Ce fichier
```

## Permissions requises

- `declarativeNetRequest` : Pour modifier les headers HTTP
- `storage` : Pour sauvegarder les préférences
- `activeTab` : Pour accéder à l'onglet actif
- `<all_urls>` : Pour appliquer les modifications sur tous les sites

## Développement

L'extension utilise :
- **Manifest V3** pour la compatibilité future
- **declarativeNetRequest API** pour modifier les headers de manière sécurisée
- **Chrome Storage API** pour la persistance des données
- **Service Worker** pour la gestion en arrière-plan

## Dépannage

### L'extension ne fonctionne pas
1. Vérifiez que le mode développeur est activé
2. Rechargez l'extension dans `chrome://extensions/`
3. Vérifiez la console pour les erreurs

### Le user agent ne change pas
1. Actualisez la page après avoir appliqué le changement
2. Vérifiez que l'extension a les permissions nécessaires
3. Testez sur un site comme `https://httpbin.org/user-agent`

## Licence

Ce projet est sous licence MIT. Vous êtes libre de l'utiliser, le modifier et le distribuer.
