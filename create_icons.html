<!DOCTYPE html>
<html>
<head>
    <title>Icon Generator</title>
</head>
<body>
    <canvas id="canvas16" width="16" height="16" style="border: 1px solid black;"></canvas>
    <canvas id="canvas48" width="48" height="48" style="border: 1px solid black;"></canvas>
    <canvas id="canvas128" width="128" height="128" style="border: 1px solid black;"></canvas>
    
    <script>
        function createIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            
            // Background
            ctx.fillStyle = '#4285f4';
            ctx.fillRect(0, 0, size, size);
            
            // Border
            ctx.strokeStyle = '#1a73e8';
            ctx.lineWidth = Math.max(1, size / 32);
            ctx.strokeRect(0, 0, size, size);
            
            // Text "UA"
            ctx.fillStyle = 'white';
            ctx.font = `bold ${size * 0.4}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('UA', size / 2, size / 2);
        }
        
        // Create icons
        createIcon(document.getElementById('canvas16'), 16);
        createIcon(document.getElementById('canvas48'), 48);
        createIcon(document.getElementById('canvas128'), 128);
        
        // Download function
        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        // Auto download after 1 second
        setTimeout(() => {
            downloadCanvas(document.getElementById('canvas16'), 'icon16.png');
            setTimeout(() => {
                downloadCanvas(document.getElementById('canvas48'), 'icon48.png');
                setTimeout(() => {
                    downloadCanvas(document.getElementById('canvas128'), 'icon128.png');
                }, 500);
            }, 500);
        }, 1000);
    </script>
</body>
</html>
