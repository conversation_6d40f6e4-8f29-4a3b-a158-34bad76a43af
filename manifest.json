{"manifest_version": 3, "name": "User Agent Switcher", "version": "1.0", "description": "Change user agent with a simple browser/OS matrix", "permissions": ["declarativeNetRequest", "storage", "activeTab", "scripting", "tabs"], "host_permissions": ["<all_urls>"], "background": {"service_worker": "background.js"}, "action": {"default_popup": "popup.html", "default_title": "User Agent Switcher"}, "icons": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["content.js"], "run_at": "document_start", "all_frames": true}]}