<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 400px;
      padding: 20px;
      font-family: Arial, sans-serif;
      background-color: #f5f5f5;
    }
    
    .header {
      text-align: center;
      margin-bottom: 20px;
      color: #333;
    }
    
    .matrix-container {
      background: white;
      border-radius: 8px;
      padding: 15px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .matrix-table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 15px;
    }
    
    .matrix-table th,
    .matrix-table td {
      padding: 8px;
      text-align: center;
      border: 1px solid #ddd;
    }
    
    .matrix-table th {
      background-color: #4285f4;
      color: white;
      font-weight: bold;
    }
    
    .browser-label {
      background-color: #f8f9fa;
      font-weight: bold;
      text-align: left;
      padding-left: 12px;
    }
    
    .radio-cell {
      background-color: #fafafa;
    }
    
    input[type="radio"] {
      transform: scale(1.2);
      cursor: pointer;
    }
    
    .controls {
      display: flex;
      gap: 10px;
      justify-content: space-between;
      margin-top: 15px;
    }
    
    .btn {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: background-color 0.3s;
    }
    
    .btn-primary {
      background-color: #4285f4;
      color: white;
    }
    
    .btn-primary:hover {
      background-color: #3367d6;
    }
    
    .btn-secondary {
      background-color: #6c757d;
      color: white;
    }
    
    .btn-secondary:hover {
      background-color: #545b62;
    }
    
    .status {
      margin-top: 10px;
      padding: 8px;
      border-radius: 4px;
      text-align: center;
      font-size: 12px;
    }
    
    .status.success {
      background-color: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    
    .status.info {
      background-color: #d1ecf1;
      color: #0c5460;
      border: 1px solid #bee5eb;
    }

    .status.error {
      background-color: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
  </style>
</head>
<body>
  <div class="header">
    <h2>User Agent Switcher</h2>
  </div>
  
  <div class="matrix-container">
    <table class="matrix-table">
      <thead>
        <tr>
          <th>Navigateur</th>
          <th>Windows</th>
          <th>macOS</th>
          <th>Linux</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td class="browser-label">Chrome</td>
          <td class="radio-cell"><input type="radio" name="useragent" value="chrome-windows"></td>
          <td class="radio-cell"><input type="radio" name="useragent" value="chrome-macos"></td>
          <td class="radio-cell"><input type="radio" name="useragent" value="chrome-linux"></td>
        </tr>
        <tr>
          <td class="browser-label">Firefox</td>
          <td class="radio-cell"><input type="radio" name="useragent" value="firefox-windows"></td>
          <td class="radio-cell"><input type="radio" name="useragent" value="firefox-macos"></td>
          <td class="radio-cell"><input type="radio" name="useragent" value="firefox-linux"></td>
        </tr>
        <tr>
          <td class="browser-label">Safari</td>
          <td class="radio-cell"><input type="radio" name="useragent" value="safari-windows" disabled></td>
          <td class="radio-cell"><input type="radio" name="useragent" value="safari-macos"></td>
          <td class="radio-cell"><input type="radio" name="useragent" value="safari-linux" disabled></td>
        </tr>
        <tr>
          <td class="browser-label">Edge</td>
          <td class="radio-cell"><input type="radio" name="useragent" value="edge-windows"></td>
          <td class="radio-cell"><input type="radio" name="useragent" value="edge-macos"></td>
          <td class="radio-cell"><input type="radio" name="useragent" value="edge-linux"></td>
        </tr>
      </tbody>
    </table>
    
    <div class="controls">
      <button id="applyBtn" class="btn btn-primary">Appliquer</button>
      <button id="resetBtn" class="btn btn-secondary">Reset</button>
    </div>
    
    <div id="status" class="status" style="display: none;"></div>
  </div>
  
  <script src="popup.js"></script>
</body>
</html>
