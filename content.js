// Content script pour modifier l'user agent
(function() {
    'use strict';
    
    // Fonction pour appliquer l'user agent
    function applyUserAgent(userAgent) {
        if (!userAgent) return;
        
        try {
            // Méthode 1: Redéfinir navigator.userAgent
            Object.defineProperty(navigator, 'userAgent', {
                get: function() { return userAgent; },
                configurable: true
            });
            
            // Méthode 2: Redéfinir navigator.appVersion
            const appVersion = userAgent.substring(userAgent.indexOf('/') + 1);
            Object.defineProperty(navigator, 'appVersion', {
                get: function() { return appVersion; },
                configurable: true
            });
            
            // Méthode 3: Modifier d'autres propriétés selon l'user agent
            updateNavigatorProperties(userAgent);
            
            console.log('User agent modifié:', userAgent);
            
        } catch (error) {
            console.error('Erreur lors de la modification de l\'user agent:', error);
        }
    }
    
    // Mettre à jour les propriétés du navigateur selon l'user agent
    function updateNavigatorProperties(userAgent) {
        try {
            // Détecter le navigateur et l'OS
            const isChrome = userAgent.includes('Chrome') && !userAgent.includes('Edg');
            const isFirefox = userAgent.includes('Firefox');
            const isSafari = userAgent.includes('Safari') && !userAgent.includes('Chrome');
            const isEdge = userAgent.includes('Edg');
            
            const isWindows = userAgent.includes('Windows');
            const isMac = userAgent.includes('Macintosh') || userAgent.includes('Mac OS');
            const isLinux = userAgent.includes('Linux');
            
            // Modifier navigator.platform
            let platform = 'Unknown';
            if (isWindows) platform = 'Win32';
            else if (isMac) platform = 'MacIntel';
            else if (isLinux) platform = 'Linux x86_64';
            
            Object.defineProperty(navigator, 'platform', {
                get: function() { return platform; },
                configurable: true
            });
            
            // Modifier navigator.vendor
            let vendor = '';
            if (isChrome || isEdge) vendor = 'Google Inc.';
            else if (isSafari) vendor = 'Apple Computer, Inc.';
            
            Object.defineProperty(navigator, 'vendor', {
                get: function() { return vendor; },
                configurable: true
            });
            
            // Modifier navigator.appName
            let appName = 'Netscape';
            if (isFirefox) appName = 'Netscape';
            else if (isChrome || isEdge || isSafari) appName = 'Netscape';
            
            Object.defineProperty(navigator, 'appName', {
                get: function() { return appName; },
                configurable: true
            });
            
        } catch (error) {
            console.error('Erreur lors de la mise à jour des propriétés:', error);
        }
    }
    
    // Écouter les messages du background script
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        if (message.action === 'setUserAgent') {
            applyUserAgent(message.userAgent);
            sendResponse({ success: true });
        } else if (message.action === 'resetUserAgent') {
            // Recharger la page pour restaurer l'user agent original
            window.location.reload();
            sendResponse({ success: true });
        }
    });
    
    // Vérifier s'il y a un user agent sauvegardé au chargement
    chrome.storage.sync.get(['selectedUserAgent']).then(result => {
        if (result.selectedUserAgent) {
            // Récupérer l'user agent correspondant
            const userAgents = {
                'chrome-windows': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'chrome-macos': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'chrome-linux': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'firefox-windows': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
                'firefox-macos': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:121.0) Gecko/20100101 Firefox/121.0',
                'firefox-linux': 'Mozilla/5.0 (X11; Linux x86_64; rv:121.0) Gecko/20100101 Firefox/121.0',
                'safari-macos': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
                'edge-windows': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
                'edge-macos': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
                'edge-linux': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0'
            };
            
            const userAgent = userAgents[result.selectedUserAgent];
            if (userAgent) {
                applyUserAgent(userAgent);
            }
        }
    }).catch(error => {
        console.error('Erreur lors de la récupération de l\'user agent:', error);
    });
    
})();
